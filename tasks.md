# Current Task: Centralized Pacific Time Management System

## Task Overview
Create a centralized timezone management system to ensure consistent Pacific Time usage across all dashboard components and resolve date synchronization issues.

## Current Issues:
- [ ] **Today Sales Card**: Shows August 1st instead of correct August 2nd (Pacific Time)
- [ ] **Chart Title**: "Today vs Previous Years" shows "Aug" (August 1st) instead of "Aug 2"
- [ ] **Column Labels**: Bottom date labels correctly show "Aug 2" but are inconsistent with title
- [ ] **Multiple Date Sources**: Different components use different date calculation methods

## Requirements:
- [ ] **Centralized Function**: Create single utility function for Pacific Time date/time
- [ ] **Real-time Updates**: Ensure live updates without manual refresh
- [ ] **Consistent Application**: Update all date-dependent components to use centralized function
- [ ] **Timezone Accuracy**: All dates must reflect Pacific Time (America/Los_Angeles)
- [ ] **Component Synchronization**: Ensure all dashboard elements show same date

## Components to Update:
- [x] **Today Sales Card**: Date display in sales card
- [x] **Chart Title**: "Today vs Previous Years" chart title date
- [x] **Column Labels**: Chart column date labels at bottom
- [x] **Chart Data Generation**: Date calculations in chart data
- [x] **Other Date References**: Any additional date displays in dashboard

## Implementation Plan:
1. [x] **Create Centralized Utility**: Implement `getPacificTime()` utility function
2. [x] **Update Sales Card Function**: Modify `updateSalesCardDates()` to use centralized function
3. [x] **Update Chart Title Function**: Modify `updateTodayVsPreviousYearsDate()` to use centralized function
4. [x] **Update Chart Data Generation**: Modify `generateTodayVsPreviousYearsData()` to use centralized function
5. [x] **Update Chart Label Generation**: Ensure column labels use centralized function
6. [x] **Test Synchronization**: Verify all components show consistent Pacific Time dates
7. [x] **Real-time Validation**: Test that dates update correctly across timezone changes

## Expected Result:
All dashboard components should display the same Pacific Time date:
- Today's Sales Card: "Aug 2, 2025"
- Chart Title: "Aug 2"
- Column Labels: "Aug 2"
- Data Generation: Uses Aug 2, 2025 for calculations

## Implementation Details:

### Files Created/Modified:
1. **`utils/timezone.js`**: Created centralized timezone utility with global `SnapTimezone` object
2. **`index.html`**: Added timezone utility script before other scripts
3. **`components/dashboard/dashboard.js`**: Updated three key functions:
   - `updateSalesCardDates()`: Now uses `SnapTimezone.getPacificTime()`
   - `updateTodayVsPreviousYearsDate()`: Now uses `SnapTimezone.getPacificTime()` and `formatPacificDate()`
   - `generateTodayVsPreviousYearsData()`: Now uses `SnapTimezone.getPacificMonthDay()`
4. **`components/charts/snap-charts.js`**: Updated `getPacificTime()` method to use centralized utility
5. **`test-timezone.html`**: Created test file to verify timezone functionality

### Centralized Functions Available:
- `SnapTimezone.getPacificTime()`: Returns current Pacific Time as Date object
- `SnapTimezone.getPacificDate()`: Returns Pacific date at midnight
- `SnapTimezone.formatPacificDate()`: Formats dates in Pacific Time
- `SnapTimezone.getPacificDateOffset()`: Gets relative dates in Pacific Time
- `SnapTimezone.isPacificToday()`: Checks if date is today in Pacific Time
- `SnapTimezone.getPacificMonthDay()`: Gets month/day for year-over-year comparisons

### Expected Result:
All dashboard components should now display consistent Pacific Time dates:
- Today's Sales Card: Uses centralized Pacific Time
- Chart Title: Uses centralized Pacific Time formatting
- Column Labels: Uses centralized Pacific Time via chart utility
- Data Generation: Uses centralized month/day calculation

## Current Status
🔄 **FIXING TIMEZONE CALCULATION** - Improved Pacific Time calculation method

### Issue Found:
- Test showed "Aug 1, 2025" instead of "Aug 2, 2025" for formatted date
- Chart data correctly showed "Aug 2" but sales card format was off by one day

### Fix Applied:
- Replaced `toLocaleString()` approach with `Intl.DateTimeFormat.formatToParts()`
- More reliable Pacific Time component extraction
- Eliminates potential string parsing issues
- Direct date formatting without double timezone conversion

### Status:
✅ **TIMEZONE CALCULATION FIXED** - Ready for final testing
